'use client';

import React from 'react';

const Footer = () => {
  const handleLinkClick = (linkName: string) => {
    console.log(`Link do footer clicado: ${linkName}`);
    alert(`Navegando para: ${linkName}`);
  };

  const handleSocialClick = (platform: string) => {
    console.log(`Rede social clicada: ${platform}`);
    alert(`Abrindo ${platform} em nova aba`);
  };

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Newsletter subscription submitted');
    alert('Obrigado por se inscrever! Em breve você receberá nossas novidades.');
  };

  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">AP</span>
              </div>
              <span className="ml-2 text-xl font-bold">AgendaPro</span>
            </div>
            <p className="text-gray-400 mb-6 leading-relaxed">
              A plataforma completa para transformar a gestão do seu estabelecimento. 
              Agendamentos, pagamentos e crescimento em um só lugar.
            </p>
            <div className="flex space-x-4">
              <button
                onClick={() => handleSocialClick('Facebook')}
                className="w-10 h-10 bg-gray-800 hover:bg-blue-600 rounded-lg flex items-center justify-center transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
              </button>
              <button
                onClick={() => handleSocialClick('Instagram')}
                className="w-10 h-10 bg-gray-800 hover:bg-pink-600 rounded-lg flex items-center justify-center transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.418-3.323c.928-.875 2.026-1.365 3.323-1.365s2.448.49 3.323 1.365c.928.875 1.418 2.026 1.418 3.323s-.49 2.448-1.418 3.244c-.875.807-2.026 1.297-3.323 1.297zm7.83-9.405c-.49 0-.928-.438-.928-.928 0-.49.438-.928.928-.928.49 0 .928.438.928.928 0 .49-.438.928-.928.928zm-4.262 1.364c-1.297 0-2.343 1.046-2.343 2.343s1.046 2.343 2.343 2.343 2.343-1.046 2.343-2.343-1.046-2.343-2.343-2.343z"/>
                </svg>
              </button>
              <button
                onClick={() => handleSocialClick('LinkedIn')}
                className="w-10 h-10 bg-gray-800 hover:bg-blue-700 rounded-lg flex items-center justify-center transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                </svg>
              </button>
              <button
                onClick={() => handleSocialClick('YouTube')}
                className="w-10 h-10 bg-gray-800 hover:bg-red-600 rounded-lg flex items-center justify-center transition-colors"
              >
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
              </button>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Produto</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => handleLinkClick('Funcionalidades')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Funcionalidades
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Planos e Preços')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Planos e Preços
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Integrações')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Integrações
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('API')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  API
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Atualizações')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Atualizações
                </button>
              </li>
            </ul>
          </div>

          {/* Support Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Suporte</h3>
            <ul className="space-y-3">
              <li>
                <button
                  onClick={() => handleLinkClick('Central de Ajuda')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Central de Ajuda
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Documentação')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Documentação
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Tutoriais')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Tutoriais
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Contato')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Contato
                </button>
              </li>
              <li>
                <button
                  onClick={() => handleLinkClick('Status do Sistema')}
                  className="text-gray-400 hover:text-white transition-colors text-left"
                >
                  Status do Sistema
                </button>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Newsletter</h3>
            <p className="text-gray-400 mb-4">
              Receba dicas, novidades e atualizações sobre gestão de estabelecimentos.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-3">
              <input
                type="email"
                placeholder="Seu melhor email"
                className="w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded-lg focus:outline-none focus:border-blue-600 text-white placeholder-gray-400"
                required
              />
              <button
                type="submit"
                className="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-semibold transition-colors"
              >
                Inscrever-se
              </button>
            </form>
            <p className="text-xs text-gray-500 mt-2">
              Não enviamos spam. Cancele quando quiser.
            </p>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-400 text-sm mb-4 md:mb-0">
              © {currentYear} AgendaPro. Todos os direitos reservados.
            </div>
            <div className="flex flex-wrap gap-6 text-sm">
              <button
                onClick={() => handleLinkClick('Política de Privacidade')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                Política de Privacidade
              </button>
              <button
                onClick={() => handleLinkClick('Termos de Uso')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                Termos de Uso
              </button>
              <button
                onClick={() => handleLinkClick('LGPD')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                LGPD
              </button>
              <button
                onClick={() => handleLinkClick('Cookies')}
                className="text-gray-400 hover:text-white transition-colors"
              >
                Cookies
              </button>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
