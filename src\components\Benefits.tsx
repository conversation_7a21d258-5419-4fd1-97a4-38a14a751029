'use client';

import React from 'react';

const Benefits = () => {
  const handleBenefitClick = (benefitTitle: string) => {
    console.log(`Benefício clicado: ${benefitTitle}`);
    alert(`Saiba mais sobre: ${benefitTitle}`);
  };

  const benefits = [
    {
      title: 'Aumente seu faturamento em até 40%',
      description: 'Com agendamentos online 24/7, seus clientes podem marcar horários a qualquer momento, aumentando significativamente suas vendas.',
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      color: 'green',
      stats: '+40% faturamento'
    },
    {
      title: 'Reduza faltas em 60%',
      description: 'Sistema inteligente de notificações e confirmações automáticas mantém seus clientes engajados e reduz drasticamente as faltas.',
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      color: 'blue',
      stats: '-60% faltas'
    },
    {
      title: 'Economize 3 horas por dia',
      description: 'Automatize agendamentos, confirmações e lembretes. Foque no que realmente importa: atender bem seus clientes.',
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
      color: 'purple',
      stats: '3h economizadas'
    },
    {
      title: 'Pagamentos garantidos',
      description: 'Receba pagamentos antecipados via Pix, cartão ou no local. Reduza inadimplência e melhore seu fluxo de caixa.',
      icon: (
        <svg className="w-12 h-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
        </svg>
      ),
      color: 'indigo',
      stats: '100% seguro'
    }
  ];

  const testimonials = [
    {
      name: 'Carlos Silva',
      business: 'Barbearia Moderna',
      location: 'São Paulo, SP',
      quote: 'Desde que comecei a usar o AgendaPro, meu faturamento aumentou 35%. Os clientes adoram poder agendar pelo celular!',
      rating: 5,
      avatar: 'CS'
    },
    {
      name: 'Ana Beatriz',
      business: 'Salão Elegance',
      location: 'Rio de Janeiro, RJ',
      quote: 'A ferramenta de relatórios me ajudou a entender melhor meu negócio. Agora sei exatamente quais serviços rendem mais.',
      rating: 5,
      avatar: 'AB'
    },
    {
      name: 'Roberto Lima',
      business: 'Clínica Estética Bella',
      location: 'Belo Horizonte, MG',
      quote: 'O sistema de marketing automático trouxe muitos clientes de volta. Recomendo para todos os colegas!',
      rating: 5,
      avatar: 'RL'
    }
  ];

  const getColorClasses = (color: string) => {
    const colorMap = {
      green: 'bg-green-100 text-green-600',
      blue: 'bg-blue-100 text-blue-600',
      purple: 'bg-purple-100 text-purple-600',
      indigo: 'bg-indigo-100 text-indigo-600'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.blue;
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header da Seção */}
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
            Resultados que
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
              {' '}transformam negócios
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Veja como o AgendaPro está ajudando estabelecimentos como o seu a 
            crescer e se destacar no mercado.
          </p>
        </div>

        {/* Grid de Benefícios */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              onClick={() => handleBenefitClick(benefit.title)}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer transform hover:-translate-y-1 group"
            >
              <div className="flex items-start">
                <div className={`flex-shrink-0 p-3 rounded-lg ${getColorClasses(benefit.color)} group-hover:scale-110 transition-transform duration-300`}>
                  {benefit.icon}
                </div>
                <div className="ml-6 flex-1">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-xl font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                      {benefit.title}
                    </h3>
                    <span className="text-sm font-bold text-green-600 bg-green-100 px-3 py-1 rounded-full">
                      {benefit.stats}
                    </span>
                  </div>
                  <p className="text-gray-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Seção de Depoimentos */}
        <div className="bg-white rounded-2xl p-8 shadow-lg">
          <div className="text-center mb-12">
            <h3 className="text-2xl font-bold text-gray-900 mb-4">
              O que nossos clientes dizem
            </h3>
            <p className="text-gray-600">
              Histórias reais de estabelecimentos que transformaram seus negócios
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={index}
                className="bg-gray-50 rounded-xl p-6 hover:bg-gray-100 transition-colors cursor-pointer"
                onClick={() => handleBenefitClick(`Depoimento de ${testimonial.name}`)}
              >
                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <svg key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                    </svg>
                  ))}
                </div>

                {/* Quote */}
                <blockquote className="text-gray-700 mb-6 italic">
                  "{testimonial.quote}"
                </blockquote>

                {/* Author */}
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                    {testimonial.avatar}
                  </div>
                  <div className="ml-4">
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    <div className="text-sm text-gray-600">{testimonial.business}</div>
                    <div className="text-xs text-gray-500">{testimonial.location}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Final */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Pronto para transformar seu negócio?
            </h3>
            <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
              Junte-se a centenas de estabelecimentos que já estão crescendo com o AgendaPro. 
              Comece hoje mesmo e veja a diferença!
            </p>
            <button
              onClick={() => handleBenefitClick('Começar Transformação')}
              className="bg-white text-blue-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              Começar Minha Transformação
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Benefits;
