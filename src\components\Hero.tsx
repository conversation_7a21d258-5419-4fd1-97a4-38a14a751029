'use client';

import React from 'react';

const Hero = () => {
  const handleStartNowClick = () => {
    console.log('Botão "Começar Agora" clicado');
    alert('Redirecionando para o processo de cadastro e escolha de plano!');
  };

  const handleViewPlansClick = () => {
    console.log('Botão "Ver Planos" clicado');
    // Simula scroll para seção de planos
    alert('Navegando para a seção de planos!');
  };

  const handleWatchDemoClick = () => {
    console.log('Botão "Ver Demonstração" clicado');
    alert('Demonstração em vídeo será exibida em breve!');
  };

  return (
    <section className="bg-gradient-to-br from-blue-50 via-white to-purple-50 pt-20 pb-16 lg:pt-24 lg:pb-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="lg:grid lg:grid-cols-12 lg:gap-8 items-center">
          {/* <PERSON><PERSON><PERSON><PERSON> Principal */}
          <div className="lg:col-span-6">
            <div className="text-center lg:text-left">
              {/* Badge */}
              <div className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 mb-6">
                <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
                Plataforma SaaS para Estabelecimentos
              </div>

              {/* Título Principal */}
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-gray-900 leading-tight mb-6">
                Transforme seu
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600">
                  {' '}agendamento{' '}
                </span>
                em sucesso
              </h1>

              {/* Subtítulo */}
              <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto lg:mx-0">
                A plataforma completa para barbearias, salões de beleza e clínicas de estética. 
                Gerencie agendamentos, pagamentos e sua equipe em um só lugar.
              </p>

              {/* Estatísticas */}
              <div className="flex flex-wrap justify-center lg:justify-start gap-6 mb-8">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">500+</div>
                  <div className="text-sm text-gray-600">Estabelecimentos</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">10k+</div>
                  <div className="text-sm text-gray-600">Agendamentos/mês</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900">98%</div>
                  <div className="text-sm text-gray-600">Satisfação</div>
                </div>
              </div>

              {/* Botões de Ação */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <button
                  onClick={handleStartNowClick}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
                >
                  Começar Agora
                </button>
                <button
                  onClick={handleViewPlansClick}
                  className="border-2 border-gray-300 hover:border-blue-600 text-gray-700 hover:text-blue-600 px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200"
                >
                  Ver Planos
                </button>
              </div>

              {/* Informação adicional */}
              <div className="mt-6 flex items-center justify-center lg:justify-start text-sm text-gray-500">
                <svg className="w-4 h-4 mr-2 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
                Sem taxa de setup • Cancele quando quiser
              </div>
            </div>
          </div>

          {/* Área Visual */}
          <div className="lg:col-span-6 mt-12 lg:mt-0">
            <div className="relative">
              {/* Mockup da Interface */}
              <div className="bg-white rounded-2xl shadow-2xl p-6 mx-auto max-w-md lg:max-w-lg">
                {/* Header do Mockup */}
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg"></div>
                    <span className="ml-2 font-semibold text-gray-900">Barbearia Moderna</span>
                  </div>
                  <div className="flex space-x-1">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                </div>

                {/* Conteúdo do Mockup */}
                <div className="space-y-4">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">Próximos Agendamentos</span>
                      <span className="text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full">+12% hoje</span>
                    </div>
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-blue-600">JS</span>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">João Silva</div>
                            <div className="text-xs text-gray-500">Corte + Barba</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">14:30</div>
                          <div className="text-xs text-green-600">Confirmado</div>
                        </div>
                      </div>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center">
                          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-medium text-purple-600">MC</span>
                          </div>
                          <div className="ml-3">
                            <div className="text-sm font-medium text-gray-900">Maria Costa</div>
                            <div className="text-xs text-gray-500">Escova + Hidratação</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">15:00</div>
                          <div className="text-xs text-yellow-600">Pendente</div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 rounded-lg p-3 text-center">
                      <div className="text-lg font-bold text-blue-600">R$ 2.450</div>
                      <div className="text-xs text-blue-600">Faturamento Hoje</div>
                    </div>
                    <div className="bg-green-50 rounded-lg p-3 text-center">
                      <div className="text-lg font-bold text-green-600">18</div>
                      <div className="text-xs text-green-600">Agendamentos</div>
                    </div>
                  </div>
                </div>

                {/* Botão Demo */}
                <button
                  onClick={handleWatchDemoClick}
                  className="w-full mt-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 rounded-lg text-sm font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200"
                >
                  Ver Demonstração
                </button>
              </div>

              {/* Elementos Decorativos */}
              <div className="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -left-4 w-16 h-16 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full opacity-20 animate-pulse delay-1000"></div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
