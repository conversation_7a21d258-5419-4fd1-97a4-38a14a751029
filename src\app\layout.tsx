import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geist<PERSON><PERSON> = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "AgendaPro - Plataforma de Agendamento para Estabelecimentos",
  description: "Transforme seu negócio com a plataforma completa de agendamento online para barbearias, salões de beleza e clínicas de estética. Gerencie agendamentos, pagamentos e sua equipe em um só lugar.",
  keywords: "agendamento online, barbearia, salão de beleza, clínica estética, gestão de estabelecimento, pagamentos online, SaaS",
  authors: [{ name: "AgendaPro" }],
  creator: "AgendaPro",
  publisher: "AgendaPro",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://agendapro.com.br'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "AgendaPro - Plataforma de Agendamento para Estabelecimentos",
    description: "Transforme seu negócio com a plataforma completa de agendamento online para barbearias, salões de beleza e clínicas de estética.",
    url: 'https://agendapro.com.br',
    siteName: 'AgendaPro',
    locale: 'pt_BR',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "AgendaPro - Plataforma de Agendamento para Estabelecimentos",
    description: "Transforme seu negócio com a plataforma completa de agendamento online para barbearias, salões de beleza e clínicas de estética.",
    creator: '@agendapro',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
